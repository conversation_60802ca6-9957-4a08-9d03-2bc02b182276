defmodule Drops.Operations.ExtensionTest do
  use Drops.OperationCase, async: false

  Code.require_file("test/support/test_extension.ex")
  Code.require_file("test/support/manual_extension.ex")

  alias Test.Support.{TestExtension, ManualExtension}

  describe "extension registration" do
    test "register/1 adds new extensions to registry" do
      defmodule Test.MyOperations do
        use Drops.Operations

        register_extension(TestExtension)
        register_extension(ManualExtension)
      end

      assert Drops.Operations.registered_extensions() == []
      assert Test.MyOperations.registered_extensions() == [TestExtension, ManualExtension]
    end
  end
end
