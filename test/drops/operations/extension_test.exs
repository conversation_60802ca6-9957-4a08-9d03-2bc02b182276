defmodule Drops.Operations.ExtensionTest do
  use Drops.OperationCase, async: false

  alias Drops.Operations.Extension

  # Ensure test extensions are compiled and available
  Code.require_file("test/support/test_extension.ex")
  Code.require_file("test/support/manual_extension.ex")

  alias Test.Support.{TestExtension, ManualExtension}

  describe "extension registration" do
    test "Operations module can be defined when application not started" do
      # Temporarily clear the persistent term to simulate app not started
      original_config = :persistent_term.get({:drops_config, :registered_extensions}, [])
      :persistent_term.erase({:drops_config, :registered_extensions})

      # This should not raise an error during compilation
      defmodule TestOperationWithoutApp do
        use Drops.Operations, repo: TestRepo
      end

      # Restore the original config
      :persistent_term.put({:drops_config, :registered_extensions}, original_config)

      # Module should be defined successfully
      assert Code.ensure_loaded?(TestOperationWithoutApp)
    end

    test "register_extension/1 macro works as alias for register/1" do
      # Create a test module that uses register_extension
      defmodule TestModuleWithRegisterExtension do
        require Drops.Operations.Extension
        Drops.Operations.Extension.register_extension(TestExtension)
      end

      # Verify the extension was registered
      extensions = Extension.get_extensions_from_module(TestModuleWithRegisterExtension)
      assert TestExtension in extensions
    end

    test "extension?/1 returns false for non-registered extensions" do
      refute Extension.extension?(NonExistentExtension)
    end

    test "register/1 macro registers extensions at compile time" do
      # Create a test module that uses the new registration API
      defmodule TestModuleWithRegistration do
        require Drops.Operations.Extension
        Drops.Operations.Extension.register(TestExtension)

        # Add a function to check the attributes were set
        def get_registered_extensions do
          case __MODULE__.__info__(:attributes) do
            attributes when is_list(attributes) ->
              Keyword.get_values(attributes, :_registered_extensions)
              |> List.flatten()

            _ ->
              []
          end
        end
      end

      # Verify the extension was registered in the module's attributes
      extensions = TestModuleWithRegistration.get_registered_extensions()
      assert TestExtension in extensions

      # Also test the helper function
      extensions_via_helper =
        Extension.get_extensions_from_module(TestModuleWithRegistration)

      assert TestExtension in extensions_via_helper
    end
  end

  describe "extension auto-discovery" do
    setup do
      Drops.Test.Config.put_test_config(
        registered_extensions: [TestExtension, ManualExtension]
      )

      :ok
    end

    test "enabled_extensions/1 includes auto-discovered extensions" do
      opts = [test_logging: true]
      enabled = Extension.enabled_extensions(opts)

      # TestExtension should be auto-discovered because test_logging is present
      assert TestExtension in enabled

      # ManualExtension should NOT be auto-discovered (always returns false)
      refute ManualExtension in enabled
    end

    test "enabled_extensions/1 excludes extensions that don't match criteria" do
      opts = [some_other_option: true]
      enabled = Extension.enabled_extensions(opts)

      # Neither extension should be enabled
      refute TestExtension in enabled
      refute ManualExtension in enabled
    end
  end

  describe "explicit extension configuration" do
    setup do
      Drops.Test.Config.put_test_config(
        registered_extensions: [TestExtension, ManualExtension]
      )

      :ok
    end

    test "enabled_extensions/1 includes explicitly configured extensions" do
      opts = [extensions: [ManualExtension]]
      enabled = Extension.enabled_extensions(opts)

      # ManualExtension should be enabled even though it doesn't auto-discover
      assert ManualExtension in enabled

      # TestExtension should NOT be enabled (not in explicit list and no test_logging)
      refute TestExtension in enabled
    end

    test "enabled_extensions/1 combines explicit and auto-discovered extensions" do
      opts = [test_logging: true, extensions: [ManualExtension]]
      enabled = Extension.enabled_extensions(opts)

      # Both extensions should be enabled
      # auto-discovered
      assert TestExtension in enabled
      # explicitly configured
      assert ManualExtension in enabled
    end

    test "enabled_extensions/1 deduplicates extensions" do
      opts = [test_logging: true, extensions: [TestExtension]]
      enabled = Extension.enabled_extensions(opts)

      # TestExtension should appear only once even though it's both auto-discovered and explicit
      assert TestExtension in enabled
      assert length(Enum.filter(enabled, &(&1 == TestExtension))) == 1
    end
  end

  describe "extension integration with operations" do
    setup do
      Drops.Test.Config.put_test_config(
        registered_extensions: [TestExtension, ManualExtension]
      )

      :ok
    end

    test "auto-discovered extension is applied to operation module" do
      defmodule TestOperationWithAutoExtension do
        use Drops.Operations, type: :command, test_logging: true

        def execute(context) do
          {:ok, context.params}
        end
      end

      # Verify the extension was loaded
      assert TestOperationWithAutoExtension.__test_extension_loaded?()
      assert function_exported?(TestOperationWithAutoExtension, :log_operation, 1)
    end

    test "explicitly configured extension is applied to operation module" do
      defmodule TestOperationWithManualExtension do
        use Drops.Operations, type: :command, extensions: [Test.Support.ManualExtension]

        def execute(context) do
          {:ok, context.params}
        end
      end

      # Verify the extension was loaded
      assert TestOperationWithManualExtension.__manual_extension_loaded?()

      assert function_exported?(
               TestOperationWithManualExtension,
               :manual_extension_active?,
               0
             )

      assert TestOperationWithManualExtension.manual_extension_active?()
    end

    test "multiple extensions can be applied together" do
      defmodule TestOperationWithBothExtensions do
        use Drops.Operations,
          type: :command,
          test_logging: true,
          extensions: [Test.Support.ManualExtension]

        def execute(context) do
          {:ok, context.params}
        end
      end

      # Verify both extensions were loaded
      assert TestOperationWithBothExtensions.__test_extension_loaded?()
      assert TestOperationWithBothExtensions.__manual_extension_loaded?()
      assert function_exported?(TestOperationWithBothExtensions, :log_operation, 1)

      assert function_exported?(
               TestOperationWithBothExtensions,
               :manual_extension_active?,
               0
             )
    end
  end
end
